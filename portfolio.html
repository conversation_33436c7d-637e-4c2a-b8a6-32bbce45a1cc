<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Portfolio - Essential Property Services</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Merriweather:ital,wght@0,400;0,700;1,400&display=swap"
      rel="stylesheet"
    />
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "#4B0082",
              accent: "#FFD700",
            },
            fontFamily: {
              sans: ['"Inter"', "system-ui", "-apple-system", "sans-serif"],
              serif: ['"Merriweather"', "Georgia", "serif"],
            },
          },
        },
      };
    </script>
    <style>
      html {
        scroll-behavior: smooth;
      }
      body {
        font-family: "Inter", system-ui, -apple-system, sans-serif;
      }

      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .animate-fadeIn {
        animation: fadeIn 0.5s ease-out forwards;
      }

      .hover-grow {
        transition: transform 0.2s ease;
      }
      .hover-grow:hover {
        transform: scale(1.02);
      }

      input:focus,
      select:focus,
      textarea:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(75, 0, 130, 0.2);
      }

      .hide-scrollbar::-webkit-scrollbar {
        display: none;
      }

      .hide-scrollbar {
        -ms-overflow-style: none;
        scrollbar-width: none;
      }

      .before-after-slider {
        position: relative;
        width: 100%;
        height: 300px;
        overflow: hidden;
        cursor: ew-resize;
        border-radius: 0.5rem;
        user-select: none;
      }

      .before-after-slider img {
        pointer-events: none;
        user-select: none;
        -webkit-user-drag: none;
      }

      .slider-handle {
        position: absolute;
        top: 50%;
        width: 32px;
        height: 32px;
        background: #ffd700;
        border-radius: 50%;
        transform: translate(-50%, -50%);
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        pointer-events: none;
      }
    </style>
    <script>
      !(function (Gleap, t, i) {
        if (!(Gleap = window.Gleap = window.Gleap || []).invoked) {
          for (
            window.GleapActions = [],
              Gleap.invoked = !0,
              Gleap.methods = [
                "identify",
                "setEnvironment",
                "setTags",
                "attachCustomData",
                "setCustomData",
                "removeCustomData",
                "clearCustomData",
                "registerCustomAction",
                "trackEvent",
                "setUseCookies",
                "log",
                "preFillForm",
                "showSurvey",
                "sendSilentCrashReport",
                "startFeedbackFlow",
                "startBot",
                "setAppBuildNumber",
                "setAppVersionCode",
                "setApiUrl",
                "setFrameUrl",
                "isOpened",
                "open",
                "close",
                "on",
                "setLanguage",
                "setOfflineMode",
                "startClassicForm",
                "initialize",
                "disableConsoleLogOverwrite",
                "logEvent",
                "hide",
                "enableShortcuts",
                "showFeedbackButton",
                "destroy",
                "getIdentity",
                "isUserIdentified",
                "clearIdentity",
                "openConversations",
                "openConversation",
                "openHelpCenterCollection",
                "openHelpCenterArticle",
                "openHelpCenter",
                "searchHelpCenter",
                "openNewsArticle",
                "openChecklists",
                "startChecklist",
                "openNews",
                "openFeatureRequests",
                "isLiveMode",
              ],
              Gleap.f = function (e) {
                return function () {
                  var t = Array.prototype.slice.call(arguments);
                  window.GleapActions.push({ e: e, a: t });
                };
              },
              t = 0;
            t < Gleap.methods.length;
            t++
          )
            Gleap[(i = Gleap.methods[t])] = Gleap.f(i);
          (Gleap.load = function () {
            var t = document.getElementsByTagName("head")[0],
              i = document.createElement("script");
            (i.type = "text/javascript"),
              (i.async = !0),
              (i.src = "https://sdk.gleap.io/latest/index.js"),
              t.appendChild(i);
          }),
            Gleap.load(),
            Gleap.initialize("AWgSKIC1b9ZgFMyt29I0VWOUUYxQSfJo");
        }
      })();
    </script>
  </head>
  <body class="font-sans text-gray-800">
    <!-- Header -->
    <header
      id="header"
      class="fixed w-full z-50 transition-all duration-300 py-6"
    >
      <div class="container mx-auto px-4 md:px-6">
        <!-- Mobile: Small floating menu on right -->
        <div class="lg:hidden flex justify-end">
          <div
            id="navContainer"
            class="backdrop-blur-sm rounded-2xl px-4 py-3 transition-all duration-300 w-auto"
          >
            <!-- Mobile Menu Button -->
            <button
              id="mobileMenuBtn"
              class="text-white"
              onclick="toggleMobileMenu()"
            >
              <svg
                class="w-7 h-7"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <line x1="3" y1="12" x2="21" y2="12"></line>
                <line x1="3" y1="6" x2="21" y2="6"></line>
                <line x1="3" y1="18" x2="21" y2="18"></line>
              </svg>
            </button>
          </div>
        </div>

        <!-- Desktop: Full width navigation -->
        <div
          id="navContainerDesktop"
          class="hidden lg:block backdrop-blur-sm rounded-2xl px-6 py-4 transition-all duration-300"
        >
          <div class="flex justify-center items-center">
            <!-- Left Navigation -->
            <div class="flex items-center space-x-8 mr-auto">
              <nav>
                <ul class="flex space-x-8">
                  <li>
                    <a
                      href="index.html"
                      class="nav-link hover:text-accent transition text-white"
                      title="Home"
                    >
                      <svg
                        class="w-6 h-6"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      >
                        <path
                          d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"
                        ></path>
                        <polyline points="9,22 9,12 15,12 15,22"></polyline>
                      </svg>
                    </a>
                  </li>
                  <li>
                    <a
                      href="services.html"
                      class="nav-link font-bold hover:text-accent transition text-white text-lg"
                      >Services</a
                    >
                  </li>
                  <li>
                    <a
                      href="portfolio.html"
                      class="nav-link font-bold hover:text-accent transition text-white text-lg"
                      >Portfolio</a
                    >
                  </li>
                  <li>
                    <a
                      href="team.html"
                      class="nav-link font-bold hover:text-accent transition text-white text-lg"
                      >Our Team</a
                    >
                  </li>
                </ul>
              </nav>
            </div>

            <!-- Right Navigation -->
            <div class="flex items-center space-x-4 ml-auto">
              <!-- Emergency Call Button -->
              <a
                href="tel:9076009900"
                class="flex items-center bg-primary hover:bg-primary/90 text-white px-4 py-3 rounded-md font-bold transition text-sm"
              >
                <svg
                  class="w-[18px] h-[18px] mr-2"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path
                    d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                  />
                </svg>
                Emergency 24/7
              </a>

              <!-- Maintenance Button -->
              <a
                href="index.html#contact"
                class="nav-link font-bold hover:bg-white/90 transition bg-white text-primary text-sm px-4 py-3 rounded-md"
                >Maintenance</a
              >

              <!-- Estimate Button -->
              <a
                href="index.html#scheduling"
                class="flex items-center bg-accent hover:bg-accent/90 text-primary px-4 py-3 rounded-md font-bold transition text-sm"
              >
                <svg
                  class="w-[18px] h-[18px] mr-2"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                  <line x1="16" y1="2" x2="16" y2="6"></line>
                  <line x1="8" y1="2" x2="8" y2="6"></line>
                  <line x1="3" y1="10" x2="21" y2="10"></line>
                </svg>
                Estimate
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Mobile menu -->
      <div
        id="mobileMenu"
        class="hidden lg:hidden bg-white/95 backdrop-blur-sm shadow-lg fixed top-0 left-0 right-0 z-50 mt-20 max-h-[calc(100vh-5rem)] overflow-y-auto rounded-b-2xl mx-4"
      >
        <nav class="px-6 py-6">
          <ul class="space-y-4">
            <li>
              <a
                href="index.html"
                class="flex items-center py-3 text-gray-800 font-medium hover:text-primary text-lg"
                onclick="closeMobileMenu()"
              >
                <svg
                  class="w-5 h-5 mr-3"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path
                    d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"
                  ></path>
                  <polyline points="9,22 9,12 15,12 15,22"></polyline>
                </svg>
                Home
              </a>
            </li>
            <li>
              <a
                href="services.html"
                class="block py-3 text-gray-800 font-medium hover:text-primary text-lg"
                onclick="closeMobileMenu()"
              >
                Services
              </a>
            </li>
            <li>
              <a
                href="portfolio.html"
                class="block py-3 text-gray-800 font-medium hover:text-primary text-lg"
                onclick="closeMobileMenu()"
              >
                Portfolio
              </a>
            </li>
            <li>
              <a
                href="team.html"
                class="block py-3 text-gray-800 font-medium hover:text-primary text-lg"
                onclick="closeMobileMenu()"
              >
                Our Team
              </a>
            </li>
            <li>
              <a
                href="index.html#contact"
                class="block py-3 text-gray-800 font-medium hover:text-primary text-lg"
                onclick="closeMobileMenu()"
              >
                Maintenance
              </a>
            </li>
            <!-- Emergency Call Button -->
            <li class="pt-4">
              <a
                href="tel:9076009900"
                class="flex items-center w-full bg-primary hover:bg-primary/90 text-white px-4 py-3 rounded-md font-bold justify-center transition"
                onclick="closeMobileMenu()"
              >
                <svg
                  class="w-[18px] h-[18px] mr-2"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path
                    d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                  />
                </svg>
                Emergency 24/7
              </a>
            </li>
            <!-- Estimate Button -->
            <li>
              <a
                href="index.html#scheduling"
                class="flex items-center w-full bg-accent hover:bg-accent/90 text-primary px-4 py-3 rounded-md font-bold justify-center transition mt-2"
                onclick="closeMobileMenu()"
              >
                <svg
                  class="w-[18px] h-[18px] mr-2"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                  <line x1="16" y1="2" x2="16" y2="6"></line>
                  <line x1="8" y1="2" x2="8" y2="6"></line>
                  <line x1="3" y1="10" x2="21" y2="10"></line>
                </svg>
                Get Estimate
              </a>
            </li>
          </ul>
        </nav>
      </div>
    </header>

    <div class="min-h-screen bg-gray-50">
      <!-- Hero Section -->
      <section class="relative h-[60vh] flex items-center">
        <div class="absolute inset-0">
          <img
            src="https://images.pexels.com/photos/1643383/pexels-photo-1643383.jpeg"
            alt="Portfolio Hero"
            class="w-full h-full object-cover"
          />
          <div class="absolute inset-0 bg-black opacity-60"></div>
        </div>
        <div class="container mx-auto px-4 relative z-10">
          <div class="max-w-4xl mx-auto text-center text-white">
            <h1 class="text-4xl md:text-5xl font-bold mb-6">
              Transforming Anchorage Homes
            </h1>
            <p class="text-xl mb-8 opacity-90">
              Browse through our portfolio of successful projects and see why
              we're Anchorage's most trusted contractor.
            </p>
            <div class="flex flex-wrap gap-4 justify-center items-center">
              <div
                class="flex items-center bg-primary/80 px-4 py-2 rounded-full"
              >
                <svg
                  class="w-5 h-5 text-accent mr-2"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <polygon
                    points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"
                  />
                </svg>
                <span class="text-white font-semibold">5/5 Average Rating</span>
              </div>
              <div
                class="flex items-center bg-primary/80 px-4 py-2 rounded-full"
              >
                <span class="text-white font-semibold"
                  >Hundreds of Happy Clients</span
                >
              </div>
              <div
                class="flex items-center bg-primary/80 px-4 py-2 rounded-full"
              >
                <span class="text-white font-semibold">Emergency Services</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Projects Grid -->
      <section class="py-20">
        <div class="container mx-auto px-4">
          <div
            class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
            id="projectsGrid"
          >
            <!-- Projects will be dynamically inserted here -->
          </div>
        </div>
      </section>

      <!-- Instagram Videos Section -->
      <section class="py-16 bg-primary/5">
        <div class="container mx-auto px-4">
          <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-primary mb-4">
              Watch Our Transformations
            </h2>
            <p class="text-gray-600">
              Follow our journey on Instagram for more inspiration
            </p>
          </div>

          <div class="relative">
            <button
              onclick="scrollVideos('left')"
              class="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white/90 p-2 rounded-full shadow-lg hover:bg-white transition-colors"
            >
              <svg
                class="w-6 h-6 text-primary"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <polyline points="15 18 9 12 15 6"></polyline>
              </svg>
            </button>

            <div
              id="videoScroll"
              class="flex overflow-x-auto gap-6 pb-6 scroll-smooth hide-scrollbar"
              style="scroll-behavior: smooth"
            >
              <!-- Videos will be dynamically inserted here -->
            </div>

            <button
              onclick="scrollVideos('right')"
              class="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white/90 p-2 rounded-full shadow-lg hover:bg-white transition-colors"
            >
              <svg
                class="w-6 h-6 text-primary"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
            </button>
          </div>
        </div>
      </section>

      <!-- CTA Section -->
      <section class="py-16 bg-primary">
        <div class="container mx-auto px-4">
          <div class="max-w-4xl mx-auto text-center text-white">
            <h2 class="text-3xl md:text-4xl font-bold mb-6">
              Ready to Start Your Dream Project?
            </h2>
            <p class="text-xl mb-8 opacity-90">
              Join our growing list of satisfied homeowners and experience the
              Essential Property Services difference.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="index.html#contact"
                class="px-8 py-4 bg-accent hover:bg-accent/90 text-primary font-semibold rounded-md transition"
              >
                Get Your Free Estimate
              </a>
              <a
                href="tel:9076009900"
                class="px-8 py-4 bg-white hover:bg-gray-100 text-primary font-semibold rounded-md transition flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 mr-2"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                  />
                </svg>
                Call Now: (*************
              </a>
            </div>
          </div>
        </div>
      </section>
    </div>

    <script>
      // Header scroll effect with transparent/white background transition
      window.addEventListener("scroll", () => {
        const header = document.getElementById("header");
        const navContainer = document.getElementById("navContainer");
        const navContainerDesktop = document.getElementById(
          "navContainerDesktop"
        );
        const navLinks = document.querySelectorAll(".nav-link");
        const mobileMenuBtn = document.getElementById("mobileMenuBtn");

        if (window.scrollY > 20) {
          // Scrolled state - white background
          header.classList.remove("py-6");
          header.classList.add("py-4");

          // Apply to both mobile and desktop containers
          if (navContainer) {
            navContainer.classList.add("bg-white/95", "shadow-lg");
          }
          if (navContainerDesktop) {
            navContainerDesktop.classList.add("bg-white/95", "shadow-lg");
          }

          // Change text colors to dark (except maintenance button which stays white bg)
          navLinks.forEach((link) => {
            if (link.textContent.trim() === "Maintenance") {
              // Maintenance button stays white with purple text
              link.classList.remove(
                "text-white",
                "hover:text-accent",
                "bg-white",
                "hover:bg-white/90"
              );
              link.classList.add(
                "text-primary",
                "bg-gray-100",
                "hover:bg-gray-200"
              );
            } else {
              link.classList.remove("text-white", "hover:text-accent");
              link.classList.add("text-gray-800", "hover:text-primary");
            }
          });

          if (mobileMenuBtn) {
            mobileMenuBtn.classList.remove("text-white");
            mobileMenuBtn.classList.add("text-primary");
          }
        } else {
          // Top of page - transparent background
          header.classList.add("py-6");
          header.classList.remove("py-4");

          // Remove from both mobile and desktop containers
          if (navContainer) {
            navContainer.classList.remove("bg-white/95", "shadow-lg");
          }
          if (navContainerDesktop) {
            navContainerDesktop.classList.remove("bg-white/95", "shadow-lg");
          }

          // Change text colors to white
          navLinks.forEach((link) => {
            if (link.textContent.trim() === "Maintenance") {
              // Maintenance button is white with purple text
              link.classList.remove(
                "text-primary",
                "bg-gray-100",
                "hover:bg-gray-200"
              );
              link.classList.add(
                "text-primary",
                "bg-white",
                "hover:bg-white/90"
              );
            } else {
              link.classList.remove("text-gray-800", "hover:text-primary");
              link.classList.add("text-white", "hover:text-accent");
            }
          });

          if (mobileMenuBtn) {
            mobileMenuBtn.classList.remove("text-primary");
            mobileMenuBtn.classList.add("text-white");
          }
        }
      });

      // Mobile menu toggle
      function toggleMobileMenu() {
        const mobileMenu = document.getElementById("mobileMenu");
        const header = document.getElementById("header");

        // Adjust mobile menu position based on header height
        const headerHeight = header.offsetHeight;
        mobileMenu.style.marginTop = headerHeight + "px";

        mobileMenu.classList.toggle("hidden");
      }

      function closeMobileMenu() {
        const mobileMenu = document.getElementById("mobileMenu");
        mobileMenu.classList.add("hidden");
      }

      // Projects data
      const projects = [
        {
          id: 1,
          title: "Baron Kitchen Transformation",
          description:
            "Complete kitchen remodel with custom cabinets, modern appliances, and beautiful finishes",
          type: "Kitchen Remodel",
          beforeImage:
            "https://images.leadconnectorhq.com/image/f_webp/q_80/r_320/u_https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af7bd2be4faeaaf3744.webp",
          afterImage:
            "https://images.leadconnectorhq.com/image/f_webp/q_80/r_320/u_https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af74a13d584d124f429.webp",
          testimonial: {
            text: "Scott and his crew are awesome. Professional in every aspect. They completed a kitchen remodel, walk-in shower, flooring, and smaller jobs for a pending sale on our home. Thank you Scott, and thank you to your outstanding crew.",
            author: "Cheryl Teal",
            rating: 5,
          },
        },
        {
          id: 2,
          title: "Luxury Shower Renovation",
          description:
            "Custom tiled walk-in shower with modern fixtures and professional installation",
          type: "Bathroom Remodel",
          beforeImage:
            "https://images.leadconnectorhq.com/image/f_webp/q_80/r_320/u_https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af7681f2d74071bd958.webp",
          afterImage:
            "https://images.leadconnectorhq.com/image/f_webp/q_80/r_320/u_https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af74a13d500e124f427.webp",
          testimonial: {
            text: "I have used Scott Cook on several projects now and APPRECIATE his responsiveness on our construction and remodel request(s). Scott also took care of a warranty item in a snap. I just referred a client this morning to Scott - he is that good.",
            author: "Brian Broderick",
            rating: 5,
          },
        },
        {
          id: 3,
          title: "Flood Damage Restoration",
          description:
            "Complete flood remediation and flooring restoration with precision and care",
          type: "Emergency Services",
          beforeImage:
            "https://images.leadconnectorhq.com/image/f_webp/q_80/r_320/u_https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af7d97df52dbc67c83e.webp",
          afterImage:
            "https://images.leadconnectorhq.com/image/f_webp/q_80/r_320/u_https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af790fc435f1db829c5.webp",
          testimonial: {
            text: "After a devastating flood damaged my home, I was in desperate need of a reliable contractor who could bring my house back to life. They worked quickly without sacrificing quality, which was especially important given the urgency after the flood. They got the job done in three days so my live in Nanny could arrive to a finished room.",
            author: "Jamie Tucker",
            rating: 5,
          },
        },
        {
          id: 4,
          title: "Window Sill Repair & Restoration",
          description:
            "Professional window sill repair and refinishing for improved functionality and appearance",
          type: "Home Maintenance",
          beforeImage:
            "https://images.leadconnectorhq.com/image/f_webp/q_80/r_320/u_https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af8681f2d348c1bd95e.webp",
          afterImage:
            "https://images.leadconnectorhq.com/image/f_webp/q_80/r_320/u_https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af76b97b26856fdc209.webp",
          testimonial: {
            text: "Scott came and helped with the issue in my kitchen. He was able to schedule me quickly and came right away. I could tell he is an honest man, and he took care of the issue correctly. He spotted the problem and fixed it.",
            author: "Amy Chase",
            rating: 5,
          },
        },
        {
          id: 5,
          title: "Complete Home Remodel",
          description:
            "Full remodel including kitchen, bathrooms, lighting, plumbing, back deck, and flooring",
          type: "Full Remodel",
          beforeImage:
            "https://images.leadconnectorhq.com/image/f_webp/q_80/r_320/u_https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af742a25329ede1cdb5.webp",
          afterImage:
            "https://images.leadconnectorhq.com/image/f_webp/q_80/r_320/u_https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af76b97b2a314fdc20a.webp",
          testimonial: {
            text: "Scott and team were the absolute best to work with. I had a full remodel to do and they knocked it out of the park. Kitchen, bathrooms, lighting, plumbing, back deck, flooring, they do it all! I was always kept in the loop and they were always ready with suggestions and options!",
            author: "KilkerJ",
            rating: 5,
          },
        },
        {
          id: 6,
          title: "Professional Painting Services",
          description:
            "Interior and exterior painting with attention to detail and quality finishes",
          type: "Painting",
          beforeImage:
            "https://images.leadconnectorhq.com/image/f_webp/q_80/r_320/u_https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af7681f2d6cfc1bd95b.webp",
          afterImage:
            "https://images.leadconnectorhq.com/image/f_webp/q_80/r_320/u_https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af7681f2d25641bd95c.webp",
          testimonial: {
            text: "These guys did an awesome job with some flooring and trimming all over the house: both living rooms, bathrooms, all doors, kitchen, around stairs and stairwells. The house has some odd angles and round corners, and they took the extra time to cut the trim to make it look flush and seamless.",
            author: "Jeremy Lussi",
            rating: 5,
          },
        },
        {
          id: 7,
          title: "Retaining Wall Rebuild",
          description:
            "Professional retaining wall reconstruction with proper drainage and structural integrity",
          type: "Structural Work",
          beforeImage:
            "https://images.leadconnectorhq.com/image/f_webp/q_80/r_320/u_https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af790fc43828ab829c6.webp",
          afterImage:
            "https://images.leadconnectorhq.com/image/f_webp/q_80/r_320/u_https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af742a253c12ce1cdb6.webp",
          testimonial: {
            text: "From various projects like deck repairs to interior work, plumbing and electrical repairs, these guys always do a great job! It was amazing how that additional bracing secured the deck. The interior cable rail looks great.",
            author: "Scott Chambers",
            rating: 5,
          },
        },
        {
          id: 8,
          title: "Excavation & Site Preparation",
          description:
            "Professional excavation services for foundation work and site preparation",
          type: "Excavation",
          beforeImage:
            "https://images.leadconnectorhq.com/image/f_webp/q_80/r_320/u_https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af74a13d59a2324f42a.webp",
          afterImage:
            "https://images.leadconnectorhq.com/image/f_webp/q_80/r_320/u_https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af790fc434dcdb829c8.webp",
          testimonial: {
            text: "We felt comfortable working with Scott from the start; he just seemed smart and competent and responsive. He diagnosed and fixed moisture damage in our basement. His communication was excellent--especially appreciated since we were out of state.",
            author: "Brent Voorhees",
            rating: 5,
          },
        },
        {
          id: 9,
          title: "Multi-Unit Property Repairs",
          description:
            "Comprehensive repairs and maintenance for rental properties and multi-unit buildings",
          type: "Property Management",
          beforeImage:
            "https://images.leadconnectorhq.com/image/f_webp/q_80/r_320/u_https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af74a13d5acb824f428.webp",
          afterImage:
            "https://images.leadconnectorhq.com/image/f_webp/q_80/r_320/u_https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68506368d97df57e336772da.webp",
          testimonial: {
            text: "We were selling our 4plex and needed some work done per the inspection report that had a tight deadline. Our realtor suggested Scott and boy am I glad he did! He was able to meet us for a quote and then arranged his schedule to get the work done within our timeline.",
            author: "Gina Wilmink",
            rating: 5,
          },
        },
      ];

      // Instagram videos data
      const instagramVideos = [
        {
          id: 1,
          thumbnail:
            "https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68152de9ca200088432f72e9.webp",
          videoUrl: "#",
          title: "Bathroom Transformation Timelapse",
          views: "2.4K views",
        },
        {
          id: 2,
          thumbnail:
            "https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68152de988cf5c168af6431b.webp",
          videoUrl: "#",
          title: "Bathroom Remodel Process",
          views: "1.8K views",
        },
        {
          id: 3,
          thumbnail:
            "https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/681468f25d4a83742002f13f.webp",
          videoUrl: "#",
          title: "Exterior Repair",
          views: "3.2K views",
        },
        {
          id: 4,
          thumbnail:
            "https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68152fe7ca200088882f750d.webp",
          videoUrl: "#",
          title: "Flood Remediation",
          views: "1.5K views",
        },
        {
          id: 5,
          thumbnail:
            "https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/681468f25d4a83484302f13c.webp",
          videoUrl: "#",
          title: "General Handyman",
          views: "2.1K views",
        },
      ];

      // Before/After Slider functionality
      class BeforeAfterSlider {
        constructor(element) {
          this.element = element;
          this.beforeImage = element.querySelector(".before-image");
          this.isDragging = false;
          this.sliderPosition = 50;

          this.element.addEventListener(
            "mousedown",
            this.startDragging.bind(this)
          );
          document.addEventListener("mousemove", this.drag.bind(this));
          document.addEventListener("mouseup", this.stopDragging.bind(this));

          // Initial render
          this.updateSliderPosition(50);
        }

        startDragging(e) {
          e.preventDefault();
          this.isDragging = true;
          this.element.style.cursor = "grabbing";
        }

        stopDragging() {
          this.isDragging = false;
          this.element.style.cursor = "ew-resize";
        }

        drag(e) {
          if (!this.isDragging) return;

          const rect = this.element.getBoundingClientRect();
          const x = Math.max(0, Math.min(e.clientX - rect.left, rect.width));
          const percent = (x / rect.width) * 100;

          this.updateSliderPosition(percent);
        }

        updateSliderPosition(percent) {
          this.sliderPosition = percent;
          this.beforeImage.style.clipPath = `inset(0 ${100 - percent}% 0 0)`;
          const handle = this.element.querySelector(".slider-handle");
          if (handle) {
            handle.style.left = `${percent}%`;
          }
        }
      }

      // Initialize projects
      function initializeProjects() {
        const projectsGrid = document.getElementById("projectsGrid");

        projects.forEach((project) => {
          const projectElement = document.createElement("div");
          projectElement.className =
            "bg-white rounded-lg shadow-lg overflow-hidden";
          projectElement.innerHTML = `
          <div class="before-after-slider">
            <img src="${
              project.afterImage
            }" alt="After" class="w-full h-full object-cover" />
            <div class="before-image absolute inset-0">
              <img src="${
                project.beforeImage
              }" alt="Before" class="w-full h-full object-cover" />
            </div>
            <div class="slider-handle">
              <svg class="w-6 h-6 text-primary" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
            </div>
          </div>
          <div class="p-6">
            <div class="flex items-center justify-between mb-3">
              <span class="text-sm font-medium text-primary">${
                project.type
              }</span>
              <div class="flex">
                ${Array(project.testimonial.rating)
                  .fill()
                  .map(
                    () => `
                  <svg class="w-4 h-4 text-primary fill-current" viewBox="0 0 24 24">
                    <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"/>
                  </svg>
                `
                  )
                  .join("")}
              </div>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-2">${
              project.title
            }</h3>
            <p class="text-gray-600 mb-4">${project.description}</p>
            <blockquote class="italic text-sm text-gray-500 border-l-4 border-primary pl-4">
              "${project.testimonial.text}"
              <footer class="mt-2 font-medium text-gray-700">
                - ${project.testimonial.author}
              </footer>
            </blockquote>
          </div>
        `;

          projectsGrid.appendChild(projectElement);
          new BeforeAfterSlider(
            projectElement.querySelector(".before-after-slider")
          );
        });
      }

      // Initialize Instagram videos
      function initializeVideos() {
        const videoScroll = document.getElementById("videoScroll");

        instagramVideos.forEach((video) => {
          const videoElement = document.createElement("div");
          videoElement.className =
            "flex-none w-[300px] bg-white rounded-lg shadow-lg overflow-hidden group";
          videoElement.innerHTML = `
          <div class="relative aspect-[9/16] bg-gray-100">
            <img
              src="${video.thumbnail}"
              alt="${video.title}"
              class="w-full h-full object-cover"
            />
            <div class="absolute inset-0 bg-primary/20 group-hover:bg-primary/30 transition-colors">
              <div class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/60 to-transparent">
                <h3 class="text-white font-semibold">${video.title}</h3>
                <p class="text-white/80 text-sm">${video.views}</p>
              </div>
            </div>
          </div>
        `;

          videoScroll.appendChild(videoElement);
        });
      }

      // Video scroll functionality
      function scrollVideos(direction) {
        const videoScroll = document.getElementById("videoScroll");
        const scrollAmount = 300;
        videoScroll.scrollLeft +=
          direction === "left" ? -scrollAmount : scrollAmount;
      }

      // Initialize everything when the page loads
      document.addEventListener("DOMContentLoaded", () => {
        initializeProjects();
        initializeVideos();
      });
    </script>
  </body>
  <!-- Footer -->
  <footer class="bg-primary text-white py-12">
    <div class="container mx-auto px-4 md:px-6">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
        <div>
          <img
            src="https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/681586cfdb0184d1a887b9de.svg"
            alt="Essential Property Services Logo"
            class="h-16 mb-4"
          />
          <p class="text-gray-300 mb-4">
            Created to serve with excellence, through humility and
            professionalism.
          </p>
          <div class="flex space-x-4">
            <a href="#" class="text-accent hover:text-accent/80 transition">
              <span class="sr-only">Facebook</span>
              <svg
                class="w-6 h-6"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path
                  d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                ></path>
              </svg>
            </a>
            <a href="#" class="text-accent hover:text-accent/80 transition">
              <span class="sr-only">Instagram</span>
              <svg
                class="w-6 h-6"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path
                  d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"
                ></path>
                <polyline points="22,6 12,13 2,6"></polyline>
              </svg>
            </a>
          </div>
        </div>

        <div>
          <h3 class="text-lg font-semibold mb-4">Contact Information</h3>
          <ul class="space-y-3">
            <li class="flex items-center">
              <svg
                class="w-[18px] h-[18px] mr-3 text-accent"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path
                  d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                ></path>
              </svg>
              <a href="tel:9076009900" class="hover:text-accent transition"
                >(*************</a
              >
            </li>
            <li class="flex items-center">
              <svg
                class="w-[18px] h-[18px] mr-3 text-accent"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path
                  d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"
                ></path>
                <polyline points="22,6 12,13 2,6"></polyline>
              </svg>
              <a
                href="mailto:<EMAIL>"
                class="hover:text-accent transition"
                ><EMAIL></a
              >
            </li>
            <li class="flex items-start">
              <svg
                class="w-[18px] h-[18px] mr-3 text-accent mt-1"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                <circle cx="12" cy="10" r="3"></circle>
              </svg>
              <span>Serving Anchorage, AK & Surrounding Areas</span>
            </li>
          </ul>
        </div>

        <div>
          <h3 class="text-lg font-semibold mb-4">Licensing & Information</h3>
          <ul class="space-y-2 text-gray-300">
            <li>Alaska General Contractor License 217482</li>
            <li>MOA License CON14200</li>
            <li>Fully Insured and Bonded</li>
            <li>Available 24/7/364</li>
          </ul>
        </div>
      </div>

      <div
        class="border-t border-gray-700 pt-8 mt-8 text-gray-300 text-sm text-center"
      >
        <p>
          ©
          <script>
            document.write(new Date().getFullYear());
          </script>
          Essential Property Services LLC. All rights reserved.
        </p>
        <p class="mt-2">
          <a href="#" class="hover:text-accent transition">Privacy Policy</a>
          {' | '}
          <a href="#" class="hover:text-accent transition">Terms of Service</a>
        </p>
      </div>
    </div>
  </footer>
</html>
