<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Essential Property Services - Anchorage's Trusted Contractor</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Merriweather:ital,wght@0,400;0,700;1,400&display=swap"
      rel="stylesheet"
    />
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "#4B0082",
              accent: "#FFD700",
            },
            fontFamily: {
              sans: ['"Inter"', "system-ui", "-apple-system", "sans-serif"],
              serif: ['"Merriweather"', "Georgia", "serif"],
            },
          },
        },
      };
    </script>
    <style>
      html {
        scroll-behavior: smooth;
      }
      body {
        font-family: "Inter", system-ui, -apple-system, sans-serif;
      }

      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .animate-fadeIn {
        animation: fadeIn 0.5s ease-out forwards;
      }

      .hover-grow {
        transition: transform 0.2s ease;
      }
      .hover-grow:hover {
        transform: scale(1.02);
      }

      input:focus,
      select:focus,
      textarea:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(75, 0, 130, 0.2);
      }
    </style>
    <script>
      !(function (Gleap, t, i) {
        if (!(Gleap = window.Gleap = window.Gleap || []).invoked) {
          for (
            window.GleapActions = [],
              Gleap.invoked = !0,
              Gleap.methods = [
                "identify",
                "setEnvironment",
                "setTags",
                "attachCustomData",
                "setCustomData",
                "removeCustomData",
                "clearCustomData",
                "registerCustomAction",
                "trackEvent",
                "setUseCookies",
                "log",
                "preFillForm",
                "showSurvey",
                "sendSilentCrashReport",
                "startFeedbackFlow",
                "startBot",
                "setAppBuildNumber",
                "setAppVersionCode",
                "setApiUrl",
                "setFrameUrl",
                "isOpened",
                "open",
                "close",
                "on",
                "setLanguage",
                "setOfflineMode",
                "startClassicForm",
                "initialize",
                "disableConsoleLogOverwrite",
                "logEvent",
                "hide",
                "enableShortcuts",
                "showFeedbackButton",
                "destroy",
                "getIdentity",
                "isUserIdentified",
                "clearIdentity",
                "openConversations",
                "openConversation",
                "openHelpCenterCollection",
                "openHelpCenterArticle",
                "openHelpCenter",
                "searchHelpCenter",
                "openNewsArticle",
                "openChecklists",
                "startChecklist",
                "openNews",
                "openFeatureRequests",
                "isLiveMode",
              ],
              Gleap.f = function (e) {
                return function () {
                  var t = Array.prototype.slice.call(arguments);
                  window.GleapActions.push({ e: e, a: t });
                };
              },
              t = 0;
            t < Gleap.methods.length;
            t++
          )
            Gleap[(i = Gleap.methods[t])] = Gleap.f(i);
          (Gleap.load = function () {
            var t = document.getElementsByTagName("head")[0],
              i = document.createElement("script");
            (i.type = "text/javascript"),
              (i.async = !0),
              (i.src = "https://sdk.gleap.io/latest/index.js"),
              t.appendChild(i);
          }),
            Gleap.load(),
            Gleap.initialize("AWgSKIC1b9ZgFMyt29I0VWOUUYxQSfJo");
        }
      })();
    </script>
  </head>
  <body class="font-sans text-gray-800">
    <!-- Header -->
    <header
      id="header"
      class="fixed w-full z-50 transition-all duration-300 py-4"
    >
      <div class="container mx-auto px-4 md:px-6">
        <div class="flex justify-between items-center">
          <div class="flex items-center">
            <img
              src="https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/681439e6dde4a49484f17fe1.webp"
              alt="Essential Property Services Logo"
              class="h-12 md:h-16"
            />
          </div>

          <div class="hidden md:flex items-center space-x-8">
            <nav>
              <ul class="flex space-x-6">
                <li>
                  <a
                    href="services.html"
                    class="nav-link font-medium hover:text-primary transition text-white"
                    >Services</a
                  >
                </li>
                <li>
                  <a
                    href="portfolio.html"
                    class="nav-link font-medium hover:text-primary transition text-white"
                    >Portfolio</a
                  >
                </li>
                <li>
                  <a
                    href="team.html"
                    class="nav-link font-medium hover:text-primary transition text-white"
                    >Our Team</a
                  >
                </li>
                <li>
                  <a
                    href="#contact"
                    class="nav-link font-medium hover:text-primary transition text-white"
                    >Contact</a
                  >
                </li>
              </ul>
            </nav>

            <a
              href="tel:9076009900"
              class="flex items-center bg-primary hover:bg-primary/90 text-white px-4 py-2 rounded-md font-medium transition"
            >
              <svg
                class="w-[18px] h-[18px] mr-2"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <path
                  d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                />
              </svg>
              (*************
            </a>
          </div>

          <button class="md:hidden text-primary" onclick="toggleMobileMenu()">
            <svg
              class="w-7 h-7"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <line x1="3" y1="12" x2="21" y2="12"></line>
              <line x1="3" y1="6" x2="21" y2="6"></line>
              <line x1="3" y1="18" x2="21" y2="18"></line>
            </svg>
          </button>
        </div>
      </div>

      <!-- Mobile menu -->
      <div
        id="mobileMenu"
        class="hidden md:hidden bg-white shadow-lg fixed top-0 left-0 right-0 z-50 mt-16 max-h-[calc(100vh-4rem)] overflow-y-auto"
      >
        <nav class="container mx-auto px-4 py-4">
          <ul class="space-y-4">
            <li>
              <a
                href="services.html"
                class="block py-2 text-gray-800 font-medium hover:text-primary"
                onclick="closeMobileMenu()"
              >
                Services
              </a>
            </li>
            <li>
              <a
                href="portfolio.html"
                class="block py-2 text-gray-800 font-medium hover:text-primary"
                onclick="closeMobileMenu()"
              >
                Portfolio
              </a>
            </li>
            <li>
              <a
                href="team.html"
                class="block py-2 text-gray-800 font-medium hover:text-primary"
                onclick="closeMobileMenu()"
              >
                Our Team
              </a>
            </li>
            <li>
              <a
                href="#contact"
                class="block py-2 text-gray-800 font-medium hover:text-primary"
                onclick="closeMobileMenu()"
              >
                Contact
              </a>
            </li>
            <li>
              <a
                href="tel:9076009900"
                class="flex items-center w-full bg-primary hover:bg-primary/90 text-white px-4 py-3 rounded-md font-medium justify-center transition"
                onclick="closeMobileMenu()"
              >
                <svg
                  class="w-[18px] h-[18px] mr-2"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path
                    d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                  />
                </svg>
                (*************
              </a>
            </li>
          </ul>
        </nav>
      </div>
    </header>

    <script>
      // Header scroll effect
      window.addEventListener("scroll", () => {
        const header = document.getElementById("header");
        const navLinks = document.querySelectorAll(".nav-link");

        if (window.scrollY > 20) {
          header.classList.remove("py-4");
          header.classList.add("bg-white", "shadow-md", "py-2");
          navLinks.forEach((link) => {
            link.classList.remove("text-white");
            link.classList.add("text-gray-800");
          });
        } else {
          header.classList.add("py-4");
          header.classList.remove("bg-white", "shadow-md", "py-2");
          navLinks.forEach((link) => {
            link.classList.add("text-white");
            link.classList.remove("text-gray-800");
          });
        }
      });

      // Mobile menu toggle
      function toggleMobileMenu() {
        const mobileMenu = document.getElementById("mobileMenu");
        const header = document.getElementById("header");

        // Adjust mobile menu position based on header height
        const headerHeight = header.offsetHeight;
        mobileMenu.style.marginTop = headerHeight + "px";

        mobileMenu.classList.toggle("hidden");
      }

      function closeMobileMenu() {
        const mobileMenu = document.getElementById("mobileMenu");
        mobileMenu.classList.add("hidden");
      }
    </script>

    <!-- Hero Section -->
    <section class="relative h-[75vh] flex items-center">
      <div class="absolute inset-0">
        <img
          src="https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/681a37336cac3426a6a6f1cd.webp"
          alt="Beautiful Alaskan Home Project"
          class="w-full h-full object-cover"
        />
        <div class="absolute inset-0 bg-black opacity-60"></div>
      </div>

      <div class="container mx-auto px-4 relative z-10">
        <div class="grid md:grid-cols-5 gap-8 items-center">
          <div class="md:col-span-3 text-white">
            <h1
              class="text-4xl md:text-5xl font-bold leading-tight mb-6 animate-fadeIn"
            >
              Anchorage's Trusted Partner for Home Repairs & Remodels
            </h1>
            <p class="text-xl mb-8 opacity-90">
              Finally, an Anchorage contractor who actually shows up,
              communicates clearly, and delivers top-notch, reliable work.
            </p>
            <div class="flex flex-col sm:flex-row gap-4">
              <a
                href="#contact"
                class="px-8 py-3 bg-accent hover:bg-accent/90 text-primary font-semibold rounded-md text-center transition"
              >
                Get Your FREE Estimate Today!
              </a>
              <a
                href="tel:9076009900"
                class="px-8 py-3 bg-white hover:bg-gray-100 text-primary font-semibold rounded-md text-center transition flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 mr-2"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path
                    d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                  />
                </svg>
                Call Now: (*************
              </a>
            </div>
          </div>

          <div class="md:col-span-2">
            <div class="bg-white rounded-lg shadow-xl p-6 max-w-sm mx-auto">
              <h3 class="text-2xl font-bold text-gray-800 mb-4">
                Get Your Free Quote
              </h3>
              <form class="space-y-4">
                <input
                  type="text"
                  placeholder="Full Name"
                  required
                  class="w-full px-4 py-2 border border-gray-300 rounded-md focus:border-primary focus:ring-1 focus:ring-primary"
                />
                <input
                  type="email"
                  placeholder="Email Address"
                  required
                  class="w-full px-4 py-2 border border-gray-300 rounded-md focus:border-primary focus:ring-1 focus:ring-primary"
                />
                <input
                  type="tel"
                  placeholder="Phone Number"
                  required
                  class="w-full px-4 py-2 border border-gray-300 rounded-md focus:border-primary focus:ring-1 focus:ring-primary"
                />
                <select
                  required
                  class="w-full px-4 py-2 border border-gray-300 rounded-md focus:border-primary focus:ring-1 focus:ring-primary"
                >
                  <option value="">Select Project Type</option>
                  <option value="full-remodel">Full Kitchen Remodel</option>
                  <option value="cabinet-refresh">Cabinet Refresh</option>
                  <option value="countertops">Countertop Replacement</option>
                  <option value="appliances">Appliance Upgrade</option>
                </select>
                <textarea
                  placeholder="Tell us about your project"
                  rows="3"
                  class="w-full px-4 py-2 border border-gray-300 rounded-md focus:border-primary focus:ring-1 focus:ring-primary"
                ></textarea>
                <button
                  type="submit"
                  class="w-full py-3 bg-primary hover:bg-primary/90 text-white font-semibold rounded-md transition"
                >
                  Request Free Quote
                </button>
              </form>

              <div class="mt-4 text-center">
                <div class="flex items-center justify-center mb-2">
                  <div class="text-accent">★★★★★</div>
                  <span class="ml-2 text-gray-700 font-medium"
                    >5/5 (26 Reviews)</span
                  >
                </div>
                <p class="text-sm text-gray-600 italic">
                  "Trusted by over 800 homeowners in our community"
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Problem/Solution Section -->
    <section class="py-20 bg-gray-50">
      <div class="container mx-auto px-4 md:px-6">
        <div class="max-w-4xl mx-auto">
          <h2
            class="text-3xl md:text-4xl font-bold text-center mb-12 text-primary"
          >
            The Essential Property Services Difference
          </h2>

          <div class="space-y-6">
            <div class="grid md:grid-cols-2 gap-4">
              <div class="bg-primary/10 rounded-lg p-6">
                <div class="flex items-center mb-3">
                  <svg
                    class="w-6 h-6 text-red-500 mr-2"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="15" y1="9" x2="9" y2="15"></line>
                    <line x1="9" y1="9" x2="15" y2="15"></line>
                  </svg>
                  <h3 class="text-lg font-semibold text-primary">
                    Other Contractors
                  </h3>
                </div>
                <p class="text-gray-700">Unreturned calls, vague timelines</p>
              </div>

              <div class="bg-accent rounded-lg p-6">
                <div class="flex items-center mb-3">
                  <svg
                    class="w-6 h-6 text-primary mr-2"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <h3 class="text-lg font-semibold text-primary">
                    Our Approach
                  </h3>
                </div>
                <p class="text-primary/80">
                  Same-day responses, clear project schedules
                </p>
              </div>
            </div>

            <div class="grid md:grid-cols-2 gap-4">
              <div class="bg-primary/10 rounded-lg p-6">
                <div class="flex items-center mb-3">
                  <svg
                    class="w-6 h-6 text-red-500 mr-2"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="15" y1="9" x2="9" y2="15"></line>
                    <line x1="9" y1="9" x2="15" y2="15"></line>
                  </svg>
                  <h3 class="text-lg font-semibold text-primary">
                    Other Contractors
                  </h3>
                </div>
                <p class="text-gray-700">Inconsistent availability</p>
              </div>

              <div class="bg-accent rounded-lg p-6">
                <div class="flex items-center mb-3">
                  <svg
                    class="w-6 h-6 text-primary mr-2"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <h3 class="text-lg font-semibold text-primary">
                    Our Approach
                  </h3>
                </div>
                <p class="text-primary/80">
                  24/7 emergency service, guaranteed arrival times
                </p>
              </div>
            </div>

            <div class="grid md:grid-cols-2 gap-4">
              <div class="bg-primary/10 rounded-lg p-6">
                <div class="flex items-center mb-3">
                  <svg
                    class="w-6 h-6 text-red-500 mr-2"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="15" y1="9" x2="9" y2="15"></line>
                    <line x1="9" y1="9" x2="15" y2="15"></line>
                  </svg>
                  <h3 class="text-lg font-semibold text-primary">
                    Other Contractors
                  </h3>
                </div>
                <p class="text-gray-700">Varying standards, rushed work</p>
              </div>

              <div class="bg-accent rounded-lg p-6">
                <div class="flex items-center mb-3">
                  <svg
                    class="w-6 h-6 text-primary mr-2"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <h3 class="text-lg font-semibold text-primary">
                    Our Approach
                  </h3>
                </div>
                <p class="text-primary/80">
                  Consistent excellence, attention to detail
                </p>
              </div>
            </div>

            <div class="grid md:grid-cols-2 gap-4">
              <div class="bg-primary/10 rounded-lg p-6">
                <div class="flex items-center mb-3">
                  <svg
                    class="w-6 h-6 text-red-500 mr-2"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="15" y1="9" x2="9" y2="15"></line>
                    <line x1="9" y1="9" x2="15" y2="15"></line>
                  </svg>
                  <h3 class="text-lg font-semibold text-primary">
                    Other Contractors
                  </h3>
                </div>
                <p class="text-gray-700">Hidden fees, unclear pricing</p>
              </div>

              <div class="bg-accent rounded-lg p-6">
                <div class="flex items-center mb-3">
                  <svg
                    class="w-6 h-6 text-primary mr-2"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <h3 class="text-lg font-semibold text-primary">
                    Our Approach
                  </h3>
                </div>
                <p class="text-primary/80">
                  Detailed quotes, no surprise charges
                </p>
              </div>
            </div>
          </div>

          <div class="mt-12 text-center">
            <p class="text-xl text-primary font-semibold">
              "Choose us the first time - over 800 satisfied Anchorage
              homeowners can't be wrong!"
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="py-20 bg-primary">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold text-accent mb-4">
            How Can We Help Your Anchorage Home Today?
          </h2>
          <p class="text-xl text-gray-200 max-w-3xl mx-auto">
            Browse through our comprehensive service offerings.
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <!-- Service 1 -->
          <div class="group">
            <div
              class="relative overflow-hidden rounded-lg shadow-lg cursor-pointer aspect-[4/3]"
            >
              <div
                class="absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-110"
                style="
                  background-image: url('https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68152dd55d4a838030067434.webp');
                "
              ></div>
              <div
                class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"
              ></div>
              <div class="absolute bottom-0 left-0 right-0 p-4 text-white">
                <h3 class="text-xl font-bold">Kitchen & Bathroom Remodels</h3>
                <p class="text-sm text-accent">Complete Remodel</p>
              </div>
            </div>
            <p class="mt-2 text-gray-200 text-sm">
              Update your most essential spaces with modern fixtures, layouts,
              and finishes.
            </p>
          </div>

          <!-- Service 2 -->
          <div class="group">
            <div
              class="relative overflow-hidden rounded-lg shadow-lg cursor-pointer aspect-[4/3]"
            >
              <div
                class="absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-110"
                style="
                  background-image: url('https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/6815325f88cf5cec7bf6496e.webp');
                "
              ></div>
              <div
                class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"
              ></div>
              <div class="absolute bottom-0 left-0 right-0 p-4 text-white">
                <h3 class="text-xl font-bold">Deck Repair & Builds</h3>
                <p class="text-sm text-accent">Outdoor Living</p>
              </div>
            </div>
            <p class="mt-2 text-gray-200 text-sm">
              Enjoy the Alaskan outdoors on a safe, beautiful deck designed to
              withstand the elements.
            </p>
          </div>

          <!-- Service 3 -->
          <div class="group">
            <div
              class="relative overflow-hidden rounded-lg shadow-lg cursor-pointer aspect-[4/3]"
            >
              <div
                class="absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-110"
                style="
                  background-image: url('https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68152dd5cf32b03f4fc2813a.webp');
                "
              ></div>
              <div
                class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"
              ></div>
              <div class="absolute bottom-0 left-0 right-0 p-4 text-white">
                <h3 class="text-xl font-bold">Plumbing & Electrical</h3>
                <p class="text-sm text-accent">Home Systems</p>
              </div>
            </div>
            <p class="mt-2 text-gray-200 text-sm">
              Fix leaks, install fixtures, and handle all your wiring and
              electrical needs safely.
            </p>
          </div>

          <!-- Service 4 -->
          <div class="group">
            <div
              class="relative overflow-hidden rounded-lg shadow-lg cursor-pointer aspect-[4/3]"
            >
              <div
                class="absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-110"
                style="
                  background-image: url('https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/6815329a5d4a832a880679fd.webp');
                "
              ></div>
              <div
                class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"
              ></div>
              <div class="absolute bottom-0 left-0 right-0 p-4 text-white">
                <h3 class="text-xl font-bold">Drywall & Painting</h3>
                <p class="text-sm text-accent">Interior Finishing</p>
              </div>
            </div>
            <p class="mt-2 text-gray-200 text-sm">
              Expert finishing services to give your home a polished,
              professional look.
            </p>
          </div>

          <!-- Service 5 -->
          <div class="group">
            <div
              class="relative overflow-hidden rounded-lg shadow-lg cursor-pointer aspect-[4/3]"
            >
              <div
                class="absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-110"
                style="
                  background-image: url('https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/681468f201c78b6d86967b77.webp');
                "
              ></div>
              <div
                class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"
              ></div>
              <div class="absolute bottom-0 left-0 right-0 p-4 text-white">
                <h3 class="text-xl font-bold">Flooring Installation</h3>
                <p class="text-sm text-accent">Floor Renovation</p>
              </div>
            </div>
            <p class="mt-2 text-gray-200 text-sm">
              Upgrade your floors with precision installation of hardwood,
              laminate, tile, and more.
            </p>
          </div>

          <!-- Service 6 -->
          <div class="group">
            <div
              class="relative overflow-hidden rounded-lg shadow-lg cursor-pointer aspect-[4/3]"
            >
              <div
                class="absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-110"
                style="
                  background-image: url('https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/681468f25d4a83484302f13c.webp');
                "
              ></div>
              <div
                class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"
              ></div>
              <div class="absolute bottom-0 left-0 right-0 p-4 text-white">
                <h3 class="text-xl font-bold">General Handyman</h3>
                <p class="text-sm text-accent">Home Maintenance</p>
              </div>
            </div>
            <p class="mt-2 text-gray-200 text-sm">
              Conquer that growing list of home repairs and maintenance tasks
              efficiently.
            </p>
          </div>
        </div>

        <div class="mt-12 text-center">
          <a
            href="#contact"
            class="inline-flex items-center px-8 py-4 bg-accent hover:bg-accent/90 text-primary font-semibold rounded-md transition-colors"
          >
            Request Your Free Estimate
          </a>
        </div>
      </div>
    </section>

    <!-- Trust Section -->
    <section id="trust" class="py-20 bg-gray-50">
      <div class="container mx-auto px-4 md:px-6">
        <div class="text-center max-w-3xl mx-auto mb-16">
          <h2 class="text-3xl md:text-4xl font-bold mb-6">
            The Contractor Anchorage Realtors & Homeowners Trust
          </h2>
          <p class="text-gray-600 text-lg">
            Our reputation across hundreds of projects in Alaska speaks for
            itself. We pride ourselves on reliability, quality, and doing the
            job right the first time.
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          <!-- Testimonial 1 -->
          <div class="bg-white rounded-lg shadow-md p-6 md:p-8">
            <div class="flex mb-4">
              <svg class="w-5 h-5 text-accent fill-current" viewBox="0 0 24 24">
                <path
                  d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                />
              </svg>
              <svg class="w-5 h-5 text-accent fill-current" viewBox="0 0 24 24">
                <path
                  d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                />
              </svg>
              <svg class="w-5 h-5 text-accent fill-current" viewBox="0 0 24 24">
                <path
                  d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                />
              </svg>
              <svg class="w-5 h-5 text-accent fill-current" viewBox="0 0 24 24">
                <path
                  d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                />
              </svg>
              <svg class="w-5 h-5 text-accent fill-current" viewBox="0 0 24 24">
                <path
                  d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                />
              </svg>
            </div>
            <p class="text-gray-700 italic mb-4">
              "As an Anchorage Realtor, having a contractor I can trust is SO
              important... He SHOWS UP!!"
            </p>
            <p class="font-semibold flex items-center">
              <svg
                class="w-4 h-4 mr-2 text-primary"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                <circle cx="12" cy="7" r="4"></circle>
              </svg>
              MJ, Key Realty Group
            </p>
          </div>

          <!-- Testimonial 2 -->
          <div class="bg-white rounded-lg shadow-md p-6 md:p-8">
            <div class="flex mb-4">
              <svg class="w-5 h-5 text-accent fill-current" viewBox="0 0 24 24">
                <path
                  d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                />
              </svg>
              <svg class="w-5 h-5 text-accent fill-current" viewBox="0 0 24 24">
                <path
                  d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                />
              </svg>
              <svg class="w-5 h-5 text-accent fill-current" viewBox="0 0 24 24">
                <path
                  d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                />
              </svg>
              <svg class="w-5 h-5 text-accent fill-current" viewBox="0 0 24 24">
                <path
                  d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                />
              </svg>
              <svg class="w-5 h-5 text-accent fill-current" viewBox="0 0 24 24">
                <path
                  d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                />
              </svg>
            </div>
            <p class="text-gray-700 italic mb-4">
              "Met our tight deadline for an Anchorage 4plex sale. Daily
              pictures, top-notch work. Highly recommend!"
            </p>
            <p class="font-semibold flex items-center">
              <svg
                class="w-4 h-4 mr-2 text-primary"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                <circle cx="12" cy="7" r="4"></circle>
              </svg>
              Gina W.
            </p>
          </div>

          <!-- Testimonial 3 -->
          <div class="bg-white rounded-lg shadow-md p-6 md:p-8">
            <div class="flex mb-4">
              <svg class="w-5 h-5 text-accent fill-current" viewBox="0 0 24 24">
                <path
                  d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                />
              </svg>
              <svg class="w-5 h-5 text-accent fill-current" viewBox="0 0 24 24">
                <path
                  d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                />
              </svg>
              <svg class="w-5 h-5 text-accent fill-current" viewBox="0 0 24 24">
                <path
                  d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                />
              </svg>
              <svg class="w-5 h-5 text-accent fill-current" viewBox="0 0 24 24">
                <path
                  d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                />
              </svg>
              <svg class="w-5 h-5 text-accent fill-current" viewBox="0 0 24 24">
                <path
                  d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                />
              </svg>
            </div>
            <p class="text-gray-700 italic mb-4">
              "The most ethical and honest General contractor I've met in
              Alaska. Communication was great and he kept me in the loop."
            </p>
            <p class="font-semibold flex items-center">
              <svg
                class="w-4 h-4 mr-2 text-primary"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                <circle cx="12" cy="7" r="4"></circle>
              </svg>
              Savanna W.
            </p>
          </div>
        </div>

        <div class="bg-primary rounded-xl shadow-xl overflow-hidden">
          <div class="p-8 md:p-12">
            <div class="max-w-4xl mx-auto text-center text-white">
              <h3 class="text-2xl md:text-3xl font-bold mb-6">
                What Sets Us Apart
              </h3>

              <div
                class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 mt-8"
              >
                <div class="flex flex-col items-center">
                  <div
                    class="bg-accent rounded-full w-16 h-16 flex items-center justify-center text-primary mb-4"
                  >
                    <svg
                      class="w-8 h-8"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path
                        d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"
                      ></path>
                      <circle cx="12" cy="7" r="4"></circle>
                    </svg>
                  </div>
                  <h4 class="text-lg font-semibold mb-2">Trust & Integrity</h4>
                  <p class="text-gray-200">
                    Honest pricing and ethical practices on every project
                  </p>
                </div>

                <div class="flex flex-col items-center">
                  <div
                    class="bg-accent rounded-full w-16 h-16 flex items-center justify-center text-primary mb-4"
                  >
                    <svg
                      class="w-8 h-8"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <rect
                        x="3"
                        y="4"
                        width="18"
                        height="18"
                        rx="2"
                        ry="2"
                      ></rect>
                      <line x1="16" y1="2" x2="16" y2="6"></line>
                      <line x1="8" y1="2" x2="8" y2="6"></line>
                      <line x1="3" y1="10" x2="21" y2="10"></line>
                    </svg>
                  </div>
                  <h4 class="text-lg font-semibold mb-2">Reliability</h4>
                  <p class="text-gray-200">
                    We show up on time, every time, and meet our deadlines
                  </p>
                </div>

                <div class="flex flex-col items-center">
                  <div
                    class="bg-accent rounded-full w-16 h-16 flex items-center justify-center text-primary mb-4"
                  >
                    <svg
                      class="w-8 h-8"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path
                        d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"
                      ></path>
                    </svg>
                  </div>
                  <h4 class="text-lg font-semibold mb-2">Communication</h4>
                  <p class="text-gray-200">
                    Clear updates throughout your project, no surprises
                  </p>
                </div>

                <div class="flex flex-col items-center">
                  <div
                    class="bg-accent rounded-full w-16 h-16 flex items-center justify-center text-primary mb-4"
                  >
                    <svg
                      class="w-8 h-8"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <polygon
                        points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"
                      ></polygon>
                    </svg>
                  </div>
                  <h4 class="text-lg font-semibold mb-2">Quality</h4>
                  <p class="text-gray-200">
                    Top-notch craftsmanship and attention to detail
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Emergency Section -->
    <section class="py-16 bg-primary text-white">
      <div class="container mx-auto px-4 md:px-6">
        <div
          class="max-w-4xl mx-auto flex flex-col md:flex-row items-center justify-between"
        >
          <div class="mb-8 md:mb-0 text-center md:text-left">
            <div class="flex items-center justify-center md:justify-start mb-4">
              <svg
                class="w-8 h-8 mr-3"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12 6 12 12 16 14"></polyline>
              </svg>
              <h2 class="text-2xl md:text-3xl font-bold">Have an Emergency?</h2>
            </div>
            <p class="text-xl mb-2">24/7 on call service available</p>
            <p class="text-lg opacity-90">
              Trust decades of experience to handle your emergency repairs
            </p>
          </div>

          <div class="flex-shrink-0">
            <a
              href="tel:9076009900"
              class="flex items-center bg-accent text-primary hover:bg-accent/90 px-6 py-4 rounded-lg text-xl font-semibold transition transform hover:scale-105"
            >
              <svg
                class="w-6 h-6 mr-3"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path
                  d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                ></path>
              </svg>
              Call: (*************
            </a>
          </div>
        </div>
      </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-20 bg-white">
      <div class="container mx-auto px-4 md:px-6">
        <div class="max-w-5xl mx-auto">
          <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold mb-6">
              Ready to Start Your Anchorage Project Without the Wait?
            </h2>
            <p class="text-gray-600 text-lg max-w-3xl mx-auto">
              Why put off those needed repairs or your dream remodel any longer?
              Get prompt, professional service right here in Anchorage.
            </p>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div class="bg-gray-50 rounded-lg shadow-md p-8">
              <h3 class="text-2xl font-bold mb-6 text-primary">Contact Us</h3>

              <div class="space-y-6">
                <div class="flex items-start">
                  <svg
                    class="w-[22px] h-[22px] text-primary mr-4 mt-1 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path
                      d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                    ></path>
                  </svg>
                  <div>
                    <h4 class="font-semibold text-lg mb-1">Call Us Directly</h4>
                    <p class="text-gray-700 mb-1">(*************</p>
                    <p class="text-sm text-gray-500">
                      Fastest response for urgent needs
                    </p>
                  </div>
                </div>

                <div class="flex items-start">
                  <svg
                    class="w-[22px] h-[22px] text-primary mr-4 mt-1 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path
                      d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"
                    ></path>
                    <polyline points="22,6 12,13 2,6"></polyline>
                  </svg>
                  <div>
                    <h4 class="font-semibold text-lg mb-1">Email Us</h4>
                    <p class="text-gray-700 mb-1"><EMAIL></p>
                    <p class="text-sm text-gray-500">
                      For detailed inquiries and quotes
                    </p>
                  </div>
                </div>

                <div class="flex items-start">
                  <svg
                    class="w-[22px] h-[22px] text-primary mr-4 mt-1 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <circle cx="12" cy="12" r="10"></circle>
                    <polyline points="12 6 12 12 16 14"></polyline>
                  </svg>
                  <div>
                    <h4 class="font-semibold text-lg mb-1">Hours</h4>
                    <p class="text-gray-700 mb-1">24/7/364</p>
                    <p class="text-sm text-gray-500">
                      Emergency services always available
                    </p>
                  </div>
                </div>
              </div>

              <div class="mt-8 pt-6 border-t border-gray-200">
                <h4 class="font-semibold text-lg mb-3">
                  Professional Credentials
                </h4>
                <ul class="space-y-2 text-gray-700">
                  <li class="flex items-center">
                    <svg
                      class="w-4 h-4 text-accent mr-2"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                      <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                    Alaska General Contractor License 217482
                  </li>
                  <li class="flex items-center">
                    <svg
                      class="w-4 h-4 text-accent mr-2"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                      <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                    MOA License CON14200
                  </li>
                  <li class="flex items-center">
                    <svg
                      class="w-4 h-4 text-accent mr-2"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                      <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                    Fully Insured and Bonded
                  </li>
                </ul>
              </div>
            </div>

            <div
              class="bg-white rounded-lg shadow-md p-8 border border-gray-200"
            >
              <h3 class="text-2xl font-bold mb-6 text-primary">
                Request Your Free Estimate
              </h3>
              <form class="space-y-4">
                <div>
                  <input
                    type="text"
                    name="name"
                    placeholder="Your Name*"
                    class="w-full px-4 py-3 border border-gray-300 rounded-md focus:border-primary focus:ring-1 focus:ring-primary"
                    required
                  />
                </div>
                <div>
                  <input
                    type="tel"
                    name="phone"
                    placeholder="Phone Number*"
                    class="w-full px-4 py-3 border border-gray-300 rounded-md focus:border-primary focus:ring-1 focus:ring-primary"
                    required
                  />
                </div>
                <div>
                  <input
                    type="email"
                    name="email"
                    placeholder="Email Address*"
                    class="w-full px-4 py-3 border border-gray-300 rounded-md focus:border-primary focus:ring-1 focus:ring-primary"
                    required
                  />
                </div>
                <div>
                  <input
                    type="text"
                    name="address"
                    placeholder="Anchorage Address/Area"
                    class="w-full px-4 py-3 border border-gray-300 rounded-md focus:border-primary focus:ring-1 focus:ring-primary"
                  />
                </div>
                <div>
                  <textarea
                    name="projectDescription"
                    placeholder="Project Description*"
                    rows="4"
                    class="w-full px-4 py-3 border border-gray-300 rounded-md focus:border-primary focus:ring-1 focus:ring-primary"
                    required
                  ></textarea>
                </div>
                <button
                  type="submit"
                  class="w-full bg-primary hover:bg-primary/90 text-white font-medium py-3 px-6 rounded-md transition"
                >
                  Submit Request
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="bg-primary text-white py-12">
      <div class="container mx-auto px-4 md:px-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
          <div>
            <img
              src="https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/681439e6dde4a49484f17fe1.webp"
              alt="Essential Property Services Logo"
              class="h-16 mb-4"
            />
            <p class="text-gray-300 mb-4">
              Created to serve with excellence, through humility and
              professionalism.
            </p>
            <div class="flex space-x-4">
              <a href="#" class="text-accent hover:text-accent/80 transition">
                <span class="sr-only">Facebook</span>
                <svg
                  class="w-6 h-6"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                  ></path>
                </svg>
              </a>
              <a href="#" class="text-accent hover:text-accent/80 transition">
                <span class="sr-only">Instagram</span>
                <svg
                  class="w-6 h-6"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"
                  ></path>
                  <polyline points="22,6 12,13 2,6"></polyline>
                </svg>
              </a>
            </div>
          </div>

          <div>
            <h3 class="text-lg font-semibold mb-4">Contact Information</h3>
            <ul class="space-y-3">
              <li class="flex items-center">
                <svg
                  class="w-[18px] h-[18px] mr-3 text-accent"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                  ></path>
                </svg>
                <a href="tel:9076009900" class="hover:text-accent transition"
                  >(*************</a
                >
              </li>
              <li class="flex items-center">
                <svg
                  class="w-[18px] h-[18px] mr-3 text-accent"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"
                  ></path>
                  <polyline points="22,6 12,13 2,6"></polyline>
                </svg>
                <a
                  href="mailto:<EMAIL>"
                  class="hover:text-accent transition"
                  ><EMAIL></a
                >
              </li>
              <li class="flex items-start">
                <svg
                  class="w-[18px] h-[18px] mr-3 text-accent mt-1"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"
                  ></path>
                  <circle cx="12" cy="10" r="3"></circle>
                </svg>
                <span>Serving Anchorage, AK & Surrounding Areas</span>
              </li>
            </ul>
          </div>

          <div>
            <h3 class="text-lg font-semibold mb-4">Licensing & Information</h3>
            <ul class="space-y-2 text-gray-300">
              <li>Alaska General Contractor License 217482</li>
              <li>MOA License CON14200</li>
              <li>Fully Insured and Bonded</li>
              <li>Available 24/7/364</li>
            </ul>
          </div>
        </div>

        <div
          class="border-t border-gray-700 pt-8 mt-8 text-gray-300 text-sm text-center"
        >
          <p>
            ©
            <script>
              document.write(new Date().getFullYear());
            </script>
            Essential Property Services LLC. All rights reserved.
          </p>
          <p class="mt-2">
            <a href="#" class="hover:text-accent transition">Privacy Policy</a>
            {' | '}
            <a href="#" class="hover:text-accent transition"
              >Terms of Service</a
            >
          </p>
        </div>
      </div>
    </footer>
  </body>
</html>
